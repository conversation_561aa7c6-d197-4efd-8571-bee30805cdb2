<?php

/**
 * Fichier de configuration d'exemple pour l'API SMMEgo
 * 
 * Copiez ce fichier vers config.php et remplissez vos vraies valeurs
 */

return [
    // Votre clé API SMMEgo
    'api_key' => 'votre_cle_api_ici',
    
    // URL de l'API (ne changez que si nécessaire)
    'api_url' => 'https://smmego.com/api/v2',
    
    // Configuration cURL (optionnel)
    'curl_options' => [
        'timeout' => 30,
        'connect_timeout' => 10,
        'user_agent' => 'SMMEgo PHP Client v1.0',
        'verify_ssl' => false, // Changez à true en production si possible
    ],
    
    // Configuration de logging (optionnel)
    'logging' => [
        'enabled' => false,
        'log_file' => __DIR__ . '/../logs/api.log',
        'log_level' => 'info', // debug, info, warning, error
    ],
    
    // Limites et timeouts
    'limits' => [
        'max_retries' => 3,
        'retry_delay' => 1, // secondes
        'rate_limit' => 60, // requêtes par minute
    ]
];
