<?php

/**
 * Classe API pour SMMEgo
 * API Client pour interagir avec l'API SMMEgo v2
 */
class SMMEgoAPI
{
    /** @var string URL de l'API */
    public $api_url = 'https://smmego.com/api/v2';

    /** @var string Votre clé API */
    public $api_key = '';

    /**
     * Constructeur
     * @param string $api_key Clé API
     */
    public function __construct($api_key = '')
    {
        $this->api_key = $api_key;
    }

    /**
     * Ajouter une commande
     * @param array $data Données de la commande
     * @return object|false Résultat de la commande
     */
    public function order($data)
    {
        $post = array_merge(['key' => $this->api_key, 'action' => 'add'], $data);
        return json_decode((string)$this->connect($post));
    }

    /**
     * Obtenir le statut de la commande
     * @param int $order_id ID de la commande
     * @return object|false Statut de la commande
     */
    public function status($order_id)
    {
        return json_decode(
            $this->connect([
                'key' => $this->api_key,
                'action' => 'status',
                'order' => $order_id
            ])
        );
    }

    /**
     * Obtenir le statut des commandes multiples
     * @param array $order_ids IDs des commandes
     * @return object|false Statuts des commandes
     */
    public function multiStatus($order_ids)
    {
        return json_decode(
            $this->connect([
                'key' => $this->api_key,
                'action' => 'status',
                'orders' => implode(",", (array)$order_ids)
            ])
        );
    }

    /**
     * Obtenir la liste des services
     * @return object|false Liste des services
     */
    public function services()
    {
        return json_decode(
            $this->connect([
                'key' => $this->api_key,
                'action' => 'services',
            ])
        );
    }

    /**
     * Recharger une commande
     * @param int $orderId ID de la commande
     * @return object|false Résultat de la recharge
     */
    public function refill(int $orderId)
    {
        return json_decode(
            $this->connect([
                'key' => $this->api_key,
                'action' => 'refill',
                'order' => $orderId,
            ])
        );
    }

    /**
     * Recharger plusieurs commandes
     * @param array $orderIds IDs des commandes
     * @return object|false Résultats des recharges
     */
    public function multiRefill(array $orderIds)
    {
        return json_decode(
            $this->connect([
                'key' => $this->api_key,
                'action' => 'refill',
                'orders' => implode(',', $orderIds),
            ]),
            true
        );
    }

    /**
     * Obtenir l'état de recharge
     * @param int $refillId ID de la recharge
     * @return object|false État de la recharge
     */
    public function refillStatus(int $refillId)
    {
        return json_decode(
            $this->connect([
                'key' => $this->api_key,
                'action' => 'refill_status',
                'refill' => $refillId,
            ])
        );
    }

    /**
     * Obtenir les états de recharge multiples
     * @param array $refillIds IDs des recharges
     * @return object|false États des recharges
     */
    public function multiRefillStatus(array $refillIds)
    {
        return json_decode(
            $this->connect([
                'key' => $this->api_key,
                'action' => 'refill_status',
                'refills' => implode(',', $refillIds),
            ]),
            true
        );
    }

    /**
     * Annuler des commandes
     * @param array $orderIds IDs des commandes à annuler
     * @return object|false Résultat de l'annulation
     */
    public function cancel(array $orderIds)
    {
        return json_decode(
            $this->connect([
                'key' => $this->api_key,
                'action' => 'cancel',
                'orders' => implode(',', $orderIds),
            ]),
            true
        );
    }

    /**
     * Obtenir le solde du compte
     * @return object|false Solde du compte
     */
    public function balance()
    {
        return json_decode(
            $this->connect([
                'key' => $this->api_key,
                'action' => 'balance',
            ])
        );
    }

    /**
     * Méthode privée pour se connecter à l'API
     * @param array $post Données POST
     * @return string|false Réponse de l'API
     */
    private function connect($post)
    {
        $_post = [];
        if (is_array($post)) {
            foreach ($post as $name => $value) {
                $_post[] = $name . '=' . urlencode($value);
            }
        }

        $ch = curl_init($this->api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        if (is_array($post)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, join('&', $_post));
        }
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/4.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        
        $result = curl_exec($ch);
        if (curl_errno($ch) != 0 && empty($result)) {
            $result = false;
        }
        curl_close($ch);
        return $result;
    }
}
