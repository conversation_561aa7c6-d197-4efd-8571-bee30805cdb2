<?php

use PHPUnit\Framework\TestCase;

require_once __DIR__ . '/../src/SMMEgoAPI.php';

class SMMEgoAPITest extends TestCase
{
    private $api;
    private $test_api_key = 'test_api_key_123';

    protected function setUp(): void
    {
        $this->api = new SMMEgoAPI($this->test_api_key);
    }

    public function testConstructor()
    {
        $api = new SMMEgoAPI('test_key');
        $this->assertEquals('test_key', $api->api_key);
    }

    public function testConstructorWithoutKey()
    {
        $api = new SMMEgoAPI();
        $this->assertEquals('', $api->api_key);
    }

    public function testApiUrl()
    {
        $this->assertEquals('https://smmego.com/api/v2', $this->api->api_url);
    }

    public function testOrderMethodExists()
    {
        $this->assertTrue(method_exists($this->api, 'order'));
    }

    public function testStatusMethodExists()
    {
        $this->assertTrue(method_exists($this->api, 'status'));
    }

    public function testMultiStatusMethodExists()
    {
        $this->assertTrue(method_exists($this->api, 'multiStatus'));
    }

    public function testServicesMethodExists()
    {
        $this->assertTrue(method_exists($this->api, 'services'));
    }

    public function testRefillMethodExists()
    {
        $this->assertTrue(method_exists($this->api, 'refill'));
    }

    public function testMultiRefillMethodExists()
    {
        $this->assertTrue(method_exists($this->api, 'multiRefill'));
    }

    public function testRefillStatusMethodExists()
    {
        $this->assertTrue(method_exists($this->api, 'refillStatus'));
    }

    public function testMultiRefillStatusMethodExists()
    {
        $this->assertTrue(method_exists($this->api, 'multiRefillStatus'));
    }

    public function testCancelMethodExists()
    {
        $this->assertTrue(method_exists($this->api, 'cancel'));
    }

    public function testBalanceMethodExists()
    {
        $this->assertTrue(method_exists($this->api, 'balance'));
    }

    public function testConnectMethodExists()
    {
        $reflection = new ReflectionClass($this->api);
        $method = $reflection->getMethod('connect');
        $this->assertTrue($method->isPrivate());
    }

    // Tests d'intégration (nécessitent une vraie clé API)
    // Ces tests sont commentés car ils nécessitent une vraie clé API
    
    /*
    public function testRealApiServices()
    {
        // Remplacez par votre vraie clé API pour tester
        $real_api = new SMMEgoAPI('your_real_api_key_here');
        $services = $real_api->services();
        
        $this->assertNotFalse($services);
        $this->assertIsArray($services);
    }

    public function testRealApiBalance()
    {
        // Remplacez par votre vraie clé API pour tester
        $real_api = new SMMEgoAPI('your_real_api_key_here');
        $balance = $real_api->balance();
        
        $this->assertNotFalse($balance);
        $this->assertIsObject($balance);
        $this->assertObjectHasAttribute('balance', $balance);
        $this->assertObjectHasAttribute('currency', $balance);
    }
    */

    public function testOrderDataStructure()
    {
        // Test de la structure des données pour une commande
        $orderData = [
            'service' => 1,
            'link' => 'https://example.com/test',
            'quantity' => 100
        ];

        $this->assertIsArray($orderData);
        $this->assertArrayHasKey('service', $orderData);
        $this->assertArrayHasKey('link', $orderData);
        $this->assertArrayHasKey('quantity', $orderData);
    }

    public function testMultiStatusDataStructure()
    {
        // Test de la structure des données pour le multi-statut
        $orderIds = [1, 2, 3];
        
        $this->assertIsArray($orderIds);
        $this->assertCount(3, $orderIds);
    }

    public function testRefillDataStructure()
    {
        // Test de la structure des données pour la recharge
        $orderIds = [1, 2];
        
        $this->assertIsArray($orderIds);
        $this->assertCount(2, $orderIds);
    }

    public function testCancelDataStructure()
    {
        // Test de la structure des données pour l'annulation
        $orderIds = [1, 2, 3];
        
        $this->assertIsArray($orderIds);
        $this->assertCount(3, $orderIds);
    }
}
