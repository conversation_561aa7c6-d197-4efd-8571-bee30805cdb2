# SMMEgo API Client PHP

Client API PHP pour interagir avec l'API SMMEgo v2 - Panneau de marketing des médias sociaux.

## Installation

### Via Composer (recommandé)

```bash
composer require smmego/api-client
```

### Installation manuelle

1. Téléchargez les fichiers
2. Incluez la classe dans votre projet :

```php
require_once 'src/SMMEgoAPI.php';
```

## Configuration

```php
require_once 'src/SMMEgoAPI.php';

// Initialisez avec votre clé API
$api = new SMMEgoAPI('votre_cle_api_ici');
```

## Utilisation

### Obtenir la liste des services

```php
$services = $api->services();
if ($services) {
    foreach ($services as $service) {
        echo "Service ID: " . $service->service . " - " . $service->name . "\n";
    }
}
```

### Vérifier le solde du compte

```php
$balance = $api->balance();
if ($balance) {
    echo "Solde: " . $balance->balance . " " . $balance->currency . "\n";
}
```

### Passer une commande

```php
// Commande basique
$order = $api->order([
    'service' => 1,
    'link' => 'https://example.com/post',
    'quantity' => 100
]);

if ($order && !isset($order->error)) {
    echo "Commande créée avec l'ID: " . $order->order . "\n";
} else {
    echo "Erreur: " . ($order->error ?? 'Erreur inconnue') . "\n";
}
```

### Vérifier le statut d'une commande

```php
$status = $api->status($order_id);
if ($status) {
    echo "Statut: " . $status->status . "\n";
    echo "Charge: " . $status->charge . "\n";
    echo "Reste: " . $status->remains . "\n";
}
```

### Vérifier le statut de plusieurs commandes

```php
$statuses = $api->multiStatus([1, 2, 3]);
if ($statuses) {
    foreach ($statuses as $order_id => $status) {
        echo "Commande $order_id: " . $status->status . "\n";
    }
}
```

## Types de commandes

### 1. Commande par défaut
```php
$order = $api->order([
    'service' => 1,
    'link' => 'https://example.com/post',
    'quantity' => 100,
    'runs' => 2,
    'interval' => 5
]);
```

### 2. Commande avec commentaires personnalisés
```php
$order = $api->order([
    'service' => 1,
    'link' => 'https://example.com/post',
    'comments' => "Super post!\nJ'adore!\nBravo!"
]);
```

### 3. Commande avec mentions personnalisées
```php
$order = $api->order([
    'service' => 1,
    'link' => 'https://example.com/post',
    'usernames' => "user1\nuser2\nuser3"
]);
```

### 4. Commande paquet
```php
$order = $api->order([
    'service' => 1,
    'link' => 'https://example.com/post'
]);
```

### 5. Commentaire avec likes
```php
$order = $api->order([
    'service' => 1,
    'link' => 'https://example.com/post',
    'quantity' => 100,
    'username' => "nom_utilisateur"
]);
```

## Gestion des recharges

### Recharger une commande
```php
$refill = $api->refill($order_id);
if ($refill && !isset($refill->error)) {
    echo "Recharge créée avec l'ID: " . $refill->refill . "\n";
}
```

### Recharger plusieurs commandes
```php
$refills = $api->multiRefill([1, 2, 3]);
if ($refills) {
    foreach ($refills as $refill) {
        echo "Recharge ID: " . $refill['refill'] . "\n";
    }
}
```

### Vérifier le statut d'une recharge
```php
$refillStatus = $api->refillStatus($refill_id);
if ($refillStatus) {
    echo "Statut de la recharge: " . $refillStatus->status . "\n";
}
```

## Annulation de commandes

```php
$result = $api->cancel([1, 2, 3]);
if ($result) {
    foreach ($result as $order_id => $status) {
        echo "Commande $order_id: " . $status . "\n";
    }
}
```

## Gestion des erreurs

```php
$result = $api->order([
    'service' => 1,
    'link' => 'https://example.com/post',
    'quantity' => 100
]);

if ($result) {
    if (isset($result->error)) {
        echo "Erreur API: " . $result->error . "\n";
    } else {
        echo "Succès! ID de commande: " . $result->order . "\n";
    }
} else {
    echo "Erreur de connexion à l'API\n";
}
```

## Exemples complets

Consultez le fichier `examples/usage_examples.php` pour des exemples d'utilisation complets.

## Exigences

- PHP 7.4 ou supérieur
- Extension cURL
- Extension JSON

## Licence

MIT License

## Support

Pour le support de l'API, contactez SMMEgo directement.
Pour les problèmes avec ce client PHP, créez une issue sur GitHub.
