<?php

require_once __DIR__ . '/../src/SMMEgoAPI.php';

/**
 * Exemples d'utilisation avancée de l'API SMMEgo
 */

class SMMEgoManager
{
    private $api;
    private $config;

    public function __construct($api_key, $config = [])
    {
        $this->api = new SMMEgoAPI($api_key);
        $this->config = array_merge([
            'max_retries' => 3,
            'retry_delay' => 1,
            'log_errors' => true
        ], $config);
    }

    /**
     * Obtenir les services avec cache
     */
    public function getServicesWithCache($cache_duration = 3600)
    {
        $cache_file = __DIR__ . '/../cache/services.json';
        
        // Vérifier si le cache existe et est valide
        if (file_exists($cache_file) && (time() - filemtime($cache_file)) < $cache_duration) {
            return json_decode(file_get_contents($cache_file));
        }

        // Récupérer les services depuis l'API
        $services = $this->api->services();
        
        if ($services) {
            // C<PERSON>er le dossier cache s'il n'existe pas
            $cache_dir = dirname($cache_file);
            if (!is_dir($cache_dir)) {
                mkdir($cache_dir, 0755, true);
            }
            
            // Sauvegarder en cache
            file_put_contents($cache_file, json_encode($services));
        }

        return $services;
    }

    /**
     * Passer une commande avec retry automatique
     */
    public function placeOrderWithRetry($orderData)
    {
        $attempts = 0;
        $maxAttempts = $this->config['max_retries'];

        while ($attempts < $maxAttempts) {
            $attempts++;
            
            try {
                $result = $this->api->order($orderData);
                
                if ($result && !isset($result->error)) {
                    $this->log("Commande réussie à la tentative $attempts: " . $result->order);
                    return $result;
                }
                
                if (isset($result->error)) {
                    $this->log("Erreur API: " . $result->error);
                    
                    // Ne pas retry pour certaines erreurs
                    if (in_array($result->error, ['Invalid service', 'Insufficient funds'])) {
                        break;
                    }
                }
                
            } catch (Exception $e) {
                $this->log("Exception: " . $e->getMessage());
            }

            if ($attempts < $maxAttempts) {
                $this->log("Tentative $attempts échouée, retry dans {$this->config['retry_delay']} secondes...");
                sleep($this->config['retry_delay']);
            }
        }

        $this->log("Échec de la commande après $maxAttempts tentatives");
        return false;
    }

    /**
     * Surveiller le statut d'une commande jusqu'à completion
     */
    public function monitorOrder($orderId, $maxWaitTime = 3600, $checkInterval = 60)
    {
        $startTime = time();
        $this->log("Début de surveillance de la commande $orderId");

        while ((time() - $startTime) < $maxWaitTime) {
            $status = $this->api->status($orderId);
            
            if ($status) {
                $this->log("Commande $orderId - Statut: {$status->status}, Reste: {$status->remains}");
                
                // Statuts finaux
                if (in_array($status->status, ['Completed', 'Canceled', 'Partial'])) {
                    $this->log("Commande $orderId terminée avec le statut: {$status->status}");
                    return $status;
                }
            }

            sleep($checkInterval);
        }

        $this->log("Timeout atteint pour la surveillance de la commande $orderId");
        return false;
    }

    /**
     * Traitement par lot de commandes
     */
    public function processBatchOrders($orders)
    {
        $results = [];
        $this->log("Début du traitement par lot de " . count($orders) . " commandes");

        foreach ($orders as $index => $orderData) {
            $this->log("Traitement de la commande " . ($index + 1) . "/" . count($orders));
            
            $result = $this->placeOrderWithRetry($orderData);
            $results[] = [
                'order_data' => $orderData,
                'result' => $result,
                'success' => $result !== false
            ];

            // Pause entre les commandes pour éviter le rate limiting
            if ($index < count($orders) - 1) {
                sleep(1);
            }
        }

        $successful = array_filter($results, function($r) { return $r['success']; });
        $this->log("Traitement par lot terminé: " . count($successful) . "/" . count($orders) . " réussies");

        return $results;
    }

    /**
     * Obtenir un rapport détaillé du compte
     */
    public function getAccountReport()
    {
        $report = [
            'timestamp' => date('Y-m-d H:i:s'),
            'balance' => null,
            'services_count' => 0,
            'recent_orders' => [],
            'errors' => []
        ];

        // Solde
        try {
            $balance = $this->api->balance();
            if ($balance) {
                $report['balance'] = $balance;
            } else {
                $report['errors'][] = 'Impossible de récupérer le solde';
            }
        } catch (Exception $e) {
            $report['errors'][] = 'Erreur solde: ' . $e->getMessage();
        }

        // Services
        try {
            $services = $this->getServicesWithCache();
            if ($services) {
                $report['services_count'] = count($services);
            } else {
                $report['errors'][] = 'Impossible de récupérer les services';
            }
        } catch (Exception $e) {
            $report['errors'][] = 'Erreur services: ' . $e->getMessage();
        }

        return $report;
    }

    /**
     * Logger simple
     */
    private function log($message)
    {
        if ($this->config['log_errors']) {
            $timestamp = date('Y-m-d H:i:s');
            echo "[$timestamp] $message\n";
        }
    }
}

// Exemple d'utilisation
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $api_key = '4b43410b20f4a4ddd929ecee40c9f4db'; // Remplacez par votre vraie clé
    
    $manager = new SMMEgoManager($api_key, [
        'max_retries' => 3,
        'retry_delay' => 2,
        'log_errors' => true
    ]);

    echo "=== Rapport du compte ===\n";
    $report = $manager->getAccountReport();
    print_r($report);

    echo "\n=== Services avec cache ===\n";
    $services = $manager->getServicesWithCache();
    if ($services) {
        echo "Nombre de services: " . count($services) . "\n";
    }

    // Exemple de commandes par lot (commenté)
    /*
    $batchOrders = [
        ['service' => 1, 'link' => 'https://example.com/post1', 'quantity' => 100],
        ['service' => 2, 'link' => 'https://example.com/post2', 'quantity' => 50],
        ['service' => 3, 'link' => 'https://example.com/post3', 'quantity' => 200]
    ];
    
    $results = $manager->processBatchOrders($batchOrders);
    print_r($results);
    */
}
