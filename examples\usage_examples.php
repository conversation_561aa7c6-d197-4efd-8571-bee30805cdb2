<?php

require_once __DIR__ . '/../src/SMMEgoAPI.php';

// Configuration
$api_key = '4b43410b20f4a4ddd929ecee40c9f4db'; // Votre clé API

// Initialiser l'API
$api = new SMMEgoAPI($api_key);

echo "=== Exemples d'utilisation de l'API SMMEgo ===\n\n";

try {
    // 1. Obtenir la liste des services
    echo "1. Récupération des services...\n";
    $services = $api->services();
    if ($services) {
        echo "Services récupérés avec succès!\n";
        echo "Nombre de services: " . count($services) . "\n\n";
    } else {
        echo "Erreur lors de la récupération des services\n\n";
    }

    // 2. Obtenir le solde du compte
    echo "2. Vérification du solde...\n";
    $balance = $api->balance();
    if ($balance) {
        echo "Solde actuel: " . $balance->balance . " " . $balance->currency . "\n\n";
    } else {
        echo "Erreur lors de la récupération du solde\n\n";
    }

    // 3. Exemples de commandes (commentés pour éviter les commandes accidentelles)
    echo "3. Exemples de commandes (commentés):\n";
    
    // Commande par défaut
    /*
    $order = $api->order([
        'service' => 1, 
        'link' => 'http://example.com/test', 
        'quantity' => 100, 
        'runs' => 2, 
        'interval' => 5
    ]);
    */
    echo "// Commande par défaut\n";
    echo "// \$order = \$api->order(['service' => 1, 'link' => 'http://example.com/test', 'quantity' => 100, 'runs' => 2, 'interval' => 5]);\n\n";

    // Commande avec commentaires personnalisés
    /*
    $order = $api->order([
        'service' => 1, 
        'link' => 'http://example.com/test', 
        'comments' => "bonne photo\ngreat photo\n:)\n;)"
    ]);
    */
    echo "// Commande avec commentaires personnalisés\n";
    echo "// \$order = \$api->order(['service' => 1, 'link' => 'http://example.com/test', 'comments' => \"bonne photo\\ngreat photo\\n:)\\n;)\"]);\n\n";

    // Commande avec liste personnalisée de mentions
    /*
    $order = $api->order([
        'service' => 1, 
        'link' => 'http://example.com/test', 
        'usernames' => "test\nexample\nfb"
    ]);
    */
    echo "// Commande avec liste personnalisée de mentions\n";
    echo "// \$order = \$api->order(['service' => 1, 'link' => 'http://example.com/test', 'usernames' => \"test\\nexample\\nfb\"]);\n\n";

    // Commande paquet
    /*
    $order = $api->order([
        'service' => 1, 
        'link' => 'http://example.com/test'
    ]);
    */
    echo "// Commande paquet\n";
    echo "// \$order = \$api->order(['service' => 1, 'link' => 'http://example.com/test']);\n\n";

    // Commentaire J'aime
    /*
    $order = $api->order([
        'service' => 1, 
        'link' => 'http://example.com/test', 
        'quantity' => 100, 
        'username' => "test"
    ]);
    */
    echo "// Commentaire J'aime\n";
    echo "// \$order = \$api->order(['service' => 1, 'link' => 'http://example.com/test', 'quantity' => 100, 'username' => \"test\"]);\n\n";

    // 4. Exemples de vérification de statut (commentés)
    echo "4. Exemples de vérification de statut:\n";
    echo "// \$status = \$api->status(\$order->order); // Statut d'une commande\n";
    echo "// \$statuses = \$api->multiStatus([1, 2, 3]); // Statuts de plusieurs commandes\n\n";

    // 5. Exemples de recharge (commentés)
    echo "5. Exemples de recharge:\n";
    echo "// \$refill = (array) \$api->multiRefill([1, 2]);\n";
    echo "// \$refillIds = array_column(\$refill, 'refill');\n";
    echo "// if (\$refillIds) {\n";
    echo "//     \$refillStatuses = \$api->multiRefillStatus(\$refillIds);\n";
    echo "// }\n\n";

} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}

echo "=== Fin des exemples ===\n";
